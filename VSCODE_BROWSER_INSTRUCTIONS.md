# Opening the Tomato Price Tracker in VSCode's Integrated Browser

## Method 1: Using Command Palette (Recommended)

1. **Open Command Palette**
   - Mac: `Cmd + Shift + P`
   - Windows/Linux: `Ctrl + Shift + P`

2. **Type and select**: `Simple Browser: Show`

3. **Enter URL**: `http://localhost:3000`

4. The browser will open in a new tab within VSCode

## Method 2: Using Preview Extension

If you have the "Preview" extension installed:

1. **Open Command Palette** (Cmd/Ctrl + Shift + P)
2. **Type**: `Preview: Open Preview to the Side`
3. **Navigate to**: `http://localhost:3000`

## Method 3: Using Live Preview Extension

If you have the "Live Preview" extension:

1. **Open Command Palette** (Cmd/Ctrl + Shift + P)
2. **Type**: `Live Preview: Show Preview (External Browser)`
3. **Enter**: `http://localhost:3000`

## Tips for VSCode Browser

- **Split View**: You can drag the browser tab to create a split view with your code
- **Refresh**: Use the refresh button in the browser toolbar or Cmd/Ctrl + R
- **DevTools**: Right-click in the browser and select "Inspect" for developer tools
- **Resize**: Drag the edges to resize the browser panel

## What You'll See

Once opened, you'll see:
- 🍅 Header with auto-refresh toggle
- 💰 Three price cards (Swiggy, Blinkit, Zepto) with live updates
- 🎯 Best deal highlighted in green
- 📊 24-hour price trend chart
- ℹ️ Information section at the bottom

The prices update every 5 seconds with dummy data to simulate real-time tracking.
