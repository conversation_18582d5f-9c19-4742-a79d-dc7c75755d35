# 🍅 Tomato Price Tracker

A real-time price tracking dashboard that compares tomato prices across Swiggy, Blinkit, and Zepto to help users find the best deals.

## 🚀 Features

- **Real-time Price Monitoring**: Tracks tomato prices across three major quick-commerce platforms
- **Best Deal Highlighting**: Automatically identifies and highlights the lowest price
- **Price History Charts**: Visual representation of price trends over the last 24 hours
- **Auto-refresh**: Updates prices automatically every 5 seconds (configurable)
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Availability Status**: Shows when items are out of stock

## 📁 Project Structure

```
tomato-price-tracker/
├── frontend/               # React frontend application
│   ├── src/
│   │   ├── components/    # React components
│   │   │   ├── PriceCard.jsx
│   │   │   └── PriceChart.jsx
│   │   ├── styles/        # CSS styles
│   │   │   ├── App.css
│   │   │   ├── PriceCard.css
│   │   │   ├── PriceChart.css
│   │   │   └── index.css
│   │   ├── App.jsx        # Main application component
│   │   └── main.jsx       # Application entry point
│   ├── index.html
│   ├── package.json
│   └── vite.config.js
├── backend/               # Backend API (to be implemented)
│   └── utils/
├── tests/                 # Test files
└── README.md
```

## 🛠️ Tech Stack

### Frontend
- **React 18** - UI library
- **Vite** - Build tool and dev server
- **CSS3** - Styling with modern features
- **Axios** - HTTP client (ready for API integration)

### Backend (Planned)
- **Python with FastAPI** - REST API
- **Playwright** - Web scraping
- **SQLite/PostgreSQL** - Database
- **WebSocket** - Real-time updates

## 🏃‍♂️ Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd tomato-price-tracker
```

2. Install frontend dependencies:
```bash
cd frontend
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:3000`

## 🎨 Frontend Features

### Price Cards
- Display current prices from each platform
- Show platform logos and names
- Highlight the best deal with a special badge
- Display last update time
- Show "Out of Stock" status when items are unavailable
- Interactive hover effects

### Price Chart
- SVG-based line chart showing 24-hour price trends
- Color-coded lines for each platform
- Interactive data points
- Responsive design with horizontal scroll on mobile
- Legend for easy platform identification

### Dashboard
- Auto-refresh toggle button
- Last update timestamp
- Best deal banner with animation
- Information section about the tracker
- Fully responsive layout

## 🔄 Current Status

The frontend is complete with dummy data generation for testing. The application simulates:
- Random price variations around ₹40/kg base price
- Occasional out-of-stock scenarios
- Real-time updates every 5 seconds
- Historical price data with wave patterns

## 🚧 Next Steps

1. **Backend Development**
   - Set up FastAPI server
   - Implement web scrapers for each platform
   - Create database schema
   - Build REST API endpoints

2. **Integration**
   - Connect frontend to backend API
   - Implement WebSocket for real-time updates
   - Add error handling and retry logic

3. **Deployment**
   - Containerize with Docker
   - Set up CI/CD pipeline
   - Deploy to cloud platform

## 📝 Notes

- Currently using dummy data for demonstration
- Prices update every 5 seconds in demo mode
- The "Order Now" buttons are placeholders
- Real implementation will require handling anti-scraping measures

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

## 📄 License

This project is open source and available under the MIT License.
